<!doctype html>
<html class="no-js" lang="en">
<head>
    <meta charset="utf-8" />
    <!--====== Title ======-->
    <title>Haraka Loan Privacy Policy</title>
    <meta name="description" content="Haraka Loan provides fast, secure, and transparent personal loan services , no bank account required, supporting M-pesa, HaloPesa, and other payment methods." />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    
    <!-- Optional: Include author information -->
    <meta name="author" content="Haraka Loan" />

    <!--Favicon Icon-->
    <link rel="shortcut icon" href="assets/images/favicon.svg" type="image/svg+xml" />

    <!--CSS Stylesheets-->
    <link rel="stylesheet" href="assets/css/animate.css" />
    <link rel="stylesheet" href="assets/css/magnific-popup.css" />
    <link rel="stylesheet" href="assets/css/slick.css" />
    <link rel="stylesheet" href="assets/css/swiper.min.css" />
    <link rel="stylesheet" href="assets/css/lineicons.css" />
    <link rel="stylesheet" href="assets/css/bootstrap.min.css" />
    <link rel="stylesheet" href="assets/css/default.css" />
    <link rel="stylesheet" href="assets/css/style.css" />

    <style>
        /* Custom content area styles */
        .custom-content {
            width: 90%;
            margin: 0 auto; /* Center the content within the width */
            padding-left: 5%;
            padding-right: 5%;
            text-align: left;
        }
        /* Ensure paragraphs do not automatically break lines to split content */
        /* Optional: To prevent long content from overflowing, add white-space property */
        /* .custom-content p {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        } */
        /* Adjust heading styles for left alignment */
        .custom-content h2,
        .custom-content h3,
        .custom-content h4 {
            margin-top: 30px;
            margin-bottom: 15px;
        }
        /* Style list items */
        .custom-content ul {
            line-height: 1.8;
            margin-bottom: 20px;
            padding-left: 20px; /* Indent list content */
        }
        /* Keep content on one line if possible (can cause overflow for very long content) */
        /* Adjust as needed based on actual content length */
    </style>
</head>
<body>
    <!--[if IE]>
    <p class="browserupgrade">
        Your browser version is outdated. Please <a href="https://browsehappy.com/">upgrade your browser</a> for a better experience and security.
    </p>
    <![endif]-->

    <!-- PRELOADER -->
    <div class="preloader">
        <div class="loader">
            <div class="ytp-spinner">
                <div class="ytp-spinner-container">
                    <div class="ytp-spinner-rotator">
                        <div class="ytp-spinner-left">
                            <div class="ytp-spinner-circle"></div>
                        </div>
                        <div class="ytp-spinner-right">
                            <div class="ytp-spinner-circle"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- HEADER area -->
    <header class="header-area">
        <div class="navbar-area">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <nav class="navbar navbar-expand-lg">
                            <a class="navbar-brand" href="index.html">
                                <img src="assets/images/logo.svg" alt="Logo" />
                            </a>
                            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                                <span class="toggler-icon"></span>
                                <span class="toggler-icon"></span>
                                <span class="toggler-icon"></span>
                            </button>
                            <div class="collapse navbar-collapse sub-menu-bar" id="navbarSupportedContent">
                                <ul id="nav" class="navbar-nav ml-auto">
                                    <li class="nav-item active">
                                        <a class="page-scroll" href="index.html">Home</a>
                                    </li>
                                </ul>
                            </div>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content Area -->
    <main>
        <!-- Privacy Policy Content -->
        <div class="custom-content">
            <br><br><br><br><h2>Privacy Policy</h2>
            <p><strong>Effective Date:</strong> September 25, 2025</p>
            <p>Thank you for choosing to use Haraka Loan (hereinafter referred to as "we" or "this application"). To protect your rights, this document explains how we collect, use, store, and protect your personal information. Please read this privacy policy carefully before using.</p>
            <h3>1. Types of Information We Collect and Its Uses</h3>
            <h4>1. Personal Information</h4>
            <p>(Including ordinary personal data and sensitive personal data, according to India's Digital Personal Data Protection Act (DPDPA, 2023) and relevant regulations)</p>
            <ul>
              <li><strong>Mobile number</strong> Usage: For account registration, identity verification, and contacting you. Legal basis: Performance of contract or your consent.</li>
              <li><strong>Name, ID number, date of birth, gender, place of birth, residence, marital status, education level</strong> Usage: For identity verification, credit assessment, and contract fulfillment. Legal basis: Necessary for contract and legitimate interests (fraud prevention and ensuring service security).</li>
              <li><strong>WhatsApp, email</strong> Usage: For communication and notifications. Legal basis: Your consent or contractual requirements.</li>
              <li><strong>Facial data (biometric data)</strong> "Sensitive personal data" usage: Only for facial recognition verification to ensure account security. Legal basis: Your explicit consent, ensuring lawful data processing.</li>
              <li><strong>Device information (such as ad ID, device name, system version, MAC address, IP address, UUID/IDFV, etc.)</strong> Usage: For device identification, optimizing app performance, risk monitoring, and fraud prevention. Legal basis: Legitimate interests (risk control) and your consent.</li>
              <li><strong>Location information</strong> Usage: For providing localized loan services and risk assessment. Legal basis: Compliance with Indian regulations and your consent.</li>
              <li><strong>Work-related information (company address, company name, phone number, monthly income)</strong> Usage: To verify employment background and evaluate repayment capacity. Legal basis: Contract fulfillment and legitimate interests.</li>
              <li><strong>Contact person information (name, relationship)</strong> Usage: For emergency contacts and anti-fraud purposes. Legal basis: Your explicit consent and legitimate interests.</li>
            </ul>
            <h4>2. Permission Requests and Usage Explanation</h4>
            <p>By agreeing, we will obtain the following permissions:</p>
            <ul>
              <li><strong>Camera permission</strong> Usage: For profile photo authentication, facial recognition verification to ensure account security.</li>
              <li><strong>Album (storage)</strong> Usage: To upload ID documents and profile pictures for identity verification.</li>
              <li><strong>Location information</strong> Usage: For offering regional matching and risk assessment.</li>
              <li><strong>Contacts</strong> Usage: For emergency contact verification and anti-fraud.</li>
            </ul>
            <h3>3. Storage and Protection of Information</h3>
            <p>We will implement security measures and procedures aligned with international standards (such as ISO/IEC 27001) and approved by the Indian central government, including technical and administrative measures, to protect your personal data from unauthorized access, disclosure, alteration, or destruction. Payment data will strictly comply with RBI regulations on localization, meaning all payment data will be stored within India and not transferred abroad. We will regularly audit security measures' effectiveness.</p>
            <h3>4. Sharing, Disclosure, and Third Parties</h3>
            <p>Except as required by law or with your authorization, we will not disclose your personal data to third parties.<br>
               - Third-party partners include fraud detection services, cloud service providers, and payment gateways. These partners will provide credit evaluation, risk control, technical support, etc., under data processing agreements to ensure your data security.<br>
               - If mandated by the Indian central government or relevant regulators, or as required by law, we may disclose your information.</p>
            <h3>5. Data Transfer</h3>
            <p>We may transfer your personal data to our servers in accordance with India's DPDPA and related laws. Data transfers will follow notices from the Indian government (such as blacklist systems) and international standards (like Standard Contractual Clauses), with necessary safeguards to protect your data. Note: Payment data must be stored domestically in India as required by RBI and is prohibited from leaving India.</p>
            <h3>6. Your Rights</h3>
            <p>According to India's DPDPA and GDPR, you have the following rights:<br>
               - Access your personal data: You can query the data we hold about you at any time;<br>
               - Correct inaccurate or incomplete data;<br>
               - Request data deletion ("Right to be Forgotten"), when no longer needed for legal or contractual purposes;<br>
               - Restrict data processing, particularly during disputes;<br>
               - Data portability: Request transfer of your data in a structured, commonly used format to yourself or a third party;<br>
               - Object to data processing: Oppose processing based on legitimate interests or purpose in certain situations;<br>
               - Withdraw consent: You can withdraw your consent at any time, which will stop relevant processing unless legally required.<br>
               - Automated decisions: You have the right to obtain human intervention if automated decision-making affects you.<br>
               You can exercise these rights by contacting our <NAME_EMAIL> or via the application.</p>
            <h3>7. Data Retention Period</h3>
            <p>We will retain your personal data only as long as necessary for the purposes outlined in this policy or as required by law:<br>
               - Account-related info: For the duration of your account validity plus the legally mandated storage period (typically 1 year);<br>
               - Transaction and payment records: At least 1 year or as mandated;<br>
               - Sensitive data such as biometric info: Deleted immediately after collection or when no longer needed, unless required by law.</p>
            <h3>8. Cookies and Similar Technologies</h3>
            <p>We use cookies and similar technologies to improve user experience. You can manage or disable cookies through your browser settings.</p>
            <h3>9. Protection of Minors</h3>
            <p>We do not intentionally collect personal data from minors under 18 without explicit parental or guardian consent. If we find minors have provided data, we will delete it immediately.</p>
            <h3>10. Policy Updates Notification</h3>
            <p>We may update this policy based on law, regulation, or business needs. Changes will be announced within the app, and prior notice will be given before they take effect.</p>
            <h3>11. Regulatory Authorities and Complaints</h3>
            <p>If you have questions or complaints regarding your personal data processing, you can contact us:</p>
            <p>Developer: Rose Bud Udyog & Viniyog Limited<br>
               Address: 33, Tollygunge, Circular Road 700053 Kolkata<br>
               Email: <EMAIL></p>
        </div>
    </main>

    <!-- Footer -->
    <footer id="footer" class="footer-area">
        <div class="footer-shape shape-1"></div>
        <div class="footer-shape shape-2"></div>
        <div class="footer-shape shape-3"></div>
        <div class="footer-shape shape-4"></div>
        <div class="footer-shape shape-5"></div>
        <div class="footer-shape shape-6"></div>
        <div class="footer-shape shape-7"></div>
        <div class="footer-shape shape-8">
            <img class="svg" src="assets/images/footer-shape.svg" alt="Shape" />
        </div>

        <div class="footer-widget pt-30 pb-80">
            <div class="container">
                <div class="row">
                    <!-- Add footer content here if needed -->
                </div>
            </div>
        </div>
        <div class="footer-copyright">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="copyright d-sm-flex justify-content-between">
                            <div class="copyright-text text-center">
                                <p class="text">Copyright © 2025 Haraka Loan. All rights reserved.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to top button -->
    <a href="#" class="back-to-top"><i class="lni lni-chevron-up"></i></a>

    <!-- JS Scripts -->
    <script src="assets/js/vendor/jquery-1.12.4.min.js"></script>
    <script src="assets/js/vendor/modernizr-3.7.1.min.js"></script>
    <script src="assets/js/popper.min.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script src="assets/js/slick.min.js"></script>
    <script src="assets/js/jquery.magnific-popup.min.js"></script>
    <script src="assets/js/swiper.min.js"></script>
    <script src="assets/js/wow.min.js"></script>
    <script src="assets/js/jquery.easing.min.js"></script>
    <script src="assets/js/scrolling-nav.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>