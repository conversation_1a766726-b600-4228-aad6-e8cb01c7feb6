/*--------------------------------

LineIcons Web Font
by: lineicons.com
-------------------------------- */
@font-face {
  font-family: 'LineIcons';
  src: url('../fonts/LineIcons.eot');
  src: url('../fonts/LineIcons.eot') format('embedded-opentype'), url('../fonts/LineIcons.woff2') format('woff2'), url('../fonts/LineIcons.woff') format('woff'), url('../fonts/LineIcons.ttf') format('truetype'), url('../fonts/LineIcons.svg') format('svg');
  font-weight: normal;
  font-style: normal;
}
/*------------------------
	base class definition
-------------------------*/
.lni {
  display: inline-block;
  font: normal normal normal 1em/1 'LineIcons';
  speak: none;
  text-transform: none;
  /* Better Font Rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/*------------------------
  change icon size
-------------------------*/
/* relative units */
.lni-sm {
  font-size: 0.8em;
}
.lni-lg {
  font-size: 1.2em;
}
/* absolute units */
.lni-16 {
  font-size: 16px;
}
.lni-32 {
  font-size: 32px;
}
/*----------------------------------
  add a square/circle background
-----------------------------------*/
.lni-bg-square,
.lni-bg-circle {
  padding: 0.35em;
  background-color: #eee;
}
.lni-bg-circle {
  border-radius: 50%;
}
/*------------------------------------
  use icons as list item markers
-------------------------------------*/
.lni-ul {
  padding-left: 0;
  list-style-type: none;
}
.lni-ul > li {
  display: flex;
  align-items: flex-start;
  line-height: 1.4;
}
.lni-ul > li > .lni {
  margin-right: 0.4em;
  line-height: inherit;
}
/*------------------------
  spinning icons
-------------------------*/
.lni-is-spinning {
  -webkit-animation: lni-spin 2s infinite linear;
  -moz-animation: lni-spin 2s infinite linear;
  animation: lni-spin 2s infinite linear;
}
@-webkit-keyframes lni-spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
  }
}
@-moz-keyframes lni-spin {
  0% {
    -moz-transform: rotate(0deg);
  }
  100% {
    -moz-transform: rotate(360deg);
  }
}
@keyframes lni-spin {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/*------------------------
  rotated/flipped icons
-------------------------*/
.lni-rotate-90 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=1);
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}
.lni-rotate-180 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.lni-rotate-270 {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
  -webkit-transform: rotate(270deg);
  -moz-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  -o-transform: rotate(270deg);
  transform: rotate(270deg);
}
.lni-flip-y {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=0);
  -webkit-transform: scale(-1, 1);
  -moz-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  -o-transform: scale(-1, 1);
  transform: scale(-1, 1);
}
.lni-flip-x {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
  -webkit-transform: scale(1, -1);
  -moz-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  -o-transform: scale(1, -1);
  transform: scale(1, -1);
}
/*------------------------
	icons
-------------------------*/

.lni-500px::before {
  content: "\ea01";
}

.lni-add-files::before {
  content: "\ea02";
}

.lni-adobe::before {
  content: "\ea04";
}

.lni-agenda::before {
  content: "\ea03";
}

.lni-airbnb::before {
  content: "\ea05";
}

.lni-alarm-clock::before {
  content: "\ea06";
}

.lni-alarm::before {
  content: "\ea07";
}

.lni-amazon-original::before {
  content: "\ea08";
}

.lni-amazon-pay::before {
  content: "\ea09";
}

.lni-amazon::before {
  content: "\ea0a";
}

.lni-ambulance::before {
  content: "\ea0b";
}

.lni-amex::before {
  content: "\ea0c";
}

.lni-anchor::before {
  content: "\ea0d";
}

.lni-android-original::before {
  content: "\ea0e";
}

.lni-android::before {
  content: "\ea0f";
}

.lni-angellist::before {
  content: "\ea10";
}

.lni-angle-double-down::before {
  content: "\ea11";
}

.lni-angle-double-left::before {
  content: "\ea12";
}

.lni-angle-double-right::before {
  content: "\ea13";
}

.lni-angle-double-up::before {
  content: "\ea14";
}

.lni-angular::before {
  content: "\ea15";
}

.lni-apartment::before {
  content: "\ea16";
}

.lni-app-store::before {
  content: "\ea17";
}

.lni-apple-pay::before {
  content: "\ea18";
}

.lni-apple::before {
  content: "\ea19";
}

.lni-archive::before {
  content: "\ea1a";
}

.lni-arrow-down-circle::before {
  content: "\ea1b";
}

.lni-arrow-down::before {
  content: "\ea1c";
}

.lni-arrow-left-circle::before {
  content: "\ea1d";
}

.lni-arrow-left::before {
  content: "\ea1e";
}

.lni-arrow-right-circle::before {
  content: "\ea1f";
}

.lni-arrow-right::before {
  content: "\ea20";
}

.lni-arrow-top-left::before {
  content: "\ea21";
}

.lni-arrow-top-right::before {
  content: "\ea22";
}

.lni-arrow-up-circle::before {
  content: "\ea23";
}

.lni-arrow-up::before {
  content: "\ea24";
}

.lni-arrows-horizontal::before {
  content: "\ea25";
}

.lni-arrows-vertical::before {
  content: "\ea26";
}

.lni-atlassian::before {
  content: "\ea27";
}

.lni-aws::before {
  content: "\ea28";
}

.lni-backward::before {
  content: "\ea29";
}

.lni-baloon::before {
  content: "\ea2a";
}

.lni-ban::before {
  content: "\ea2b";
}

.lni-bar-chart::before {
  content: "\ea2c";
}

.lni-basketball::before {
  content: "\ea2d";
}

.lni-behance-original::before {
  content: "\ea2e";
}

.lni-behance::before {
  content: "\ea2f";
}

.lni-bi-cycle::before {
  content: "\ea30";
}

.lni-bitbucket::before {
  content: "\ea31";
}

.lni-bitcoin::before {
  content: "\ea32";
}

.lni-blackboard::before {
  content: "\ea33";
}

.lni-blogger::before {
  content: "\ea34";
}

.lni-bluetooth::before {
  content: "\ea35";
}

.lni-bold::before {
  content: "\ea36";
}

.lni-bolt-alt::before {
  content: "\ea37";
}

.lni-bolt::before {
  content: "\ea38";
}

.lni-book::before {
  content: "\ea39";
}

.lni-bookmark-alt::before {
  content: "\ea3a";
}

.lni-bookmark::before {
  content: "\ea3b";
}

.lni-bootstrap::before {
  content: "\ea3c";
}

.lni-bricks::before {
  content: "\ea3d";
}

.lni-bridge::before {
  content: "\ea3e";
}

.lni-briefcase::before {
  content: "\ea3f";
}

.lni-brush-alt::before {
  content: "\ea40";
}

.lni-brush::before {
  content: "\ea41";
}

.lni-bubble::before {
  content: "\ea42";
}

.lni-bug::before {
  content: "\ea43";
}

.lni-bulb::before {
  content: "\ea44";
}

.lni-bullhorn::before {
  content: "\ea45";
}

.lni-burger::before {
  content: "\ea46";
}

.lni-bus::before {
  content: "\ea47";
}

.lni-cake::before {
  content: "\ea48";
}

.lni-calculator::before {
  content: "\ea49";
}

.lni-calendar::before {
  content: "\ea4a";
}

.lni-camera::before {
  content: "\ea4b";
}

.lni-candy-cane::before {
  content: "\ea4c";
}

.lni-candy::before {
  content: "\ea4d";
}

.lni-capsule::before {
  content: "\ea4e";
}

.lni-car-alt::before {
  content: "\ea4f";
}

.lni-car::before {
  content: "\ea50";
}

.lni-caravan::before {
  content: "\ea54";
}

.lni-cart-full::before {
  content: "\ea51";
}

.lni-cart::before {
  content: "\ea52";
}

.lni-certificate::before {
  content: "\ea53";
}

.lni-checkbox::before {
  content: "\ea55";
}

.lni-checkmark-circle::before {
  content: "\ea56";
}

.lni-chef-hat::before {
  content: "\ea57";
}

.lni-chevron-down-circle::before {
  content: "\ea58";
}

.lni-chevron-down::before {
  content: "\ea59";
}

.lni-chevron-left-circle::before {
  content: "\ea5a";
}

.lni-chevron-left::before {
  content: "\ea5b";
}

.lni-chevron-right-circle::before {
  content: "\ea5c";
}

.lni-chevron-right::before {
  content: "\ea5d";
}

.lni-chevron-up-circle::before {
  content: "\ea5e";
}

.lni-chevron-up::before {
  content: "\ea5f";
}

.lni-chrome::before {
  content: "\ea60";
}

.lni-circle-minus::before {
  content: "\ea61";
}

.lni-circle-plus::before {
  content: "\ea62";
}

.lni-clipboard::before {
  content: "\ea63";
}

.lni-close::before {
  content: "\ea64";
}

.lni-cloud-check::before {
  content: "\ea65";
}

.lni-cloud-download::before {
  content: "\ea66";
}

.lni-cloud-network::before {
  content: "\ea67";
}

.lni-cloud-sync::before {
  content: "\ea68";
}

.lni-cloud-upload::before {
  content: "\ea69";
}

.lni-cloudy-sun::before {
  content: "\ea6a";
}

.lni-code-alt::before {
  content: "\ea6c";
}

.lni-code::before {
  content: "\ea6b";
}

.lni-codepen::before {
  content: "\ea6d";
}

.lni-coffee-cup::before {
  content: "\ea6e";
}

.lni-cog::before {
  content: "\ea6f";
}

.lni-cogs::before {
  content: "\ea70";
}

.lni-coin::before {
  content: "\ea71";
}

.lni-comments-alt::before {
  content: "\ea72";
}

.lni-comments-reply::before {
  content: "\ea73";
}

.lni-comments::before {
  content: "\ea74";
}

.lni-compass::before {
  content: "\ea75";
}

.lni-construction-hammer::before {
  content: "\ea76";
}

.lni-consulting::before {
  content: "\ea77";
}

.lni-control-panel::before {
  content: "\ea78";
}

.lni-cool::before {
  content: "\ea79";
}

.lni-cpanel::before {
  content: "\ea7a";
}

.lni-creative-commons::before {
  content: "\ea7b";
}

.lni-credit-cards::before {
  content: "\ea7c";
}

.lni-crop::before {
  content: "\ea7d";
}

.lni-cross-circle::before {
  content: "\ea7e";
}

.lni-crown::before {
  content: "\ea7f";
}

.lni-css3::before {
  content: "\ea80";
}

.lni-cup::before {
  content: "\ea81";
}

.lni-customer::before {
  content: "\ea82";
}

.lni-cut::before {
  content: "\ea83";
}

.lni-dashboard::before {
  content: "\ea84";
}

.lni-database::before {
  content: "\ea85";
}

.lni-delivery::before {
  content: "\ea86";
}

.lni-dervice::before {
  content: "\ea87";
}

.lni-dev::before {
  content: "\ea88";
}

.lni-diamond-alt::before {
  content: "\ea89";
}

.lni-diamond::before {
  content: "\ea8a";
}

.lni-diners-club::before {
  content: "\ea8b";
}

.lni-dinner::before {
  content: "\ea8c";
}

.lni-direction-alt::before {
  content: "\ea8d";
}

.lni-direction-ltr::before {
  content: "\ea8e";
}

.lni-direction-rtl::before {
  content: "\ea8f";
}

.lni-direction::before {
  content: "\ea90";
}

.lni-discord::before {
  content: "\ea91";
}

.lni-discover::before {
  content: "\ea92";
}

.lni-display-alt::before {
  content: "\ea93";
}

.lni-display::before {
  content: "\ea94";
}

.lni-docker::before {
  content: "\ea95";
}

.lni-dollar::before {
  content: "\ea96";
}

.lni-domain::before {
  content: "\ea97";
}

.lni-download::before {
  content: "\ea98";
}

.lni-dribbble::before {
  content: "\ea99";
}

.lni-drop::before {
  content: "\ea9a";
}

.lni-dropbox-original::before {
  content: "\ea9b";
}

.lni-dropbox::before {
  content: "\ea9c";
}

.lni-drupal-original::before {
  content: "\ea9d";
}

.lni-drupal::before {
  content: "\ea9e";
}

.lni-dumbbell::before {
  content: "\ea9f";
}

.lni-edge::before {
  content: "\eaa0";
}

.lni-empty-file::before {
  content: "\eaa1";
}

.lni-enter::before {
  content: "\eaa2";
}

.lni-envato::before {
  content: "\eaa3";
}

.lni-envelope::before {
  content: "\eaa4";
}

.lni-eraser::before {
  content: "\eaa5";
}

.lni-euro::before {
  content: "\eaa6";
}

.lni-exit-down::before {
  content: "\eaa7";
}

.lni-exit-up::before {
  content: "\eaa8";
}

.lni-exit::before {
  content: "\eaa9";
}

.lni-eye::before {
  content: "\eaaa";
}

.lni-facebook-filled::before {
  content: "\eaab";
}

.lni-facebook-messenger::before {
  content: "\eaac";
}

.lni-facebook-original::before {
  content: "\eaad";
}

.lni-facebook-oval::before {
  content: "\eaae";
}

.lni-facebook::before {
  content: "\eaaf";
}

.lni-figma::before {
  content: "\eab0";
}

.lni-files::before {
  content: "\eab1";
}

.lni-firefox-original::before {
  content: "\eab2";
}

.lni-firefox::before {
  content: "\eab3";
}

.lni-fireworks::before {
  content: "\eab4";
}

.lni-first-aid::before {
  content: "\eab5";
}

.lni-flag-alt::before {
  content: "\eab6";
}

.lni-flag::before {
  content: "\eab7";
}

.lni-flags::before {
  content: "\eab8";
}

.lni-flickr::before {
  content: "\eab9";
}

.lni-flower::before {
  content: "\eaba";
}

.lni-folder::before {
  content: "\eabb";
}

.lni-forward::before {
  content: "\eabc";
}

.lni-frame-expand::before {
  content: "\eabd";
}

.lni-fresh-juice::before {
  content: "\eabe";
}

.lni-friendly::before {
  content: "\eabf";
}

.lni-full-screen::before {
  content: "\eac0";
}

.lni-funnel::before {
  content: "\eac1";
}

.lni-gallery::before {
  content: "\eac2";
}

.lni-game::before {
  content: "\eac3";
}

.lni-gift::before {
  content: "\eac4";
}

.lni-git::before {
  content: "\eac5";
}

.lni-github-original::before {
  content: "\eac6";
}

.lni-github::before {
  content: "\eac7";
}

.lni-goodreads::before {
  content: "\eac8";
}

.lni-google-drive::before {
  content: "\eac9";
}

.lni-google-pay::before {
  content: "\eaca";
}

.lni-google-wallet::before {
  content: "\eacb";
}

.lni-google::before {
  content: "\eacc";
}

.lni-graduation::before {
  content: "\eacd";
}

.lni-graph::before {
  content: "\eace";
}

.lni-grid-alt::before {
  content: "\eacf";
}

.lni-grid::before {
  content: "\ead0";
}

.lni-grow::before {
  content: "\ead1";
}

.lni-hacker-news::before {
  content: "\ead2";
}

.lni-hammer::before {
  content: "\ead3";
}

.lni-hand::before {
  content: "\ead4";
}

.lni-handshake::before {
  content: "\ead5";
}

.lni-happy::before {
  content: "\ead6";
}

.lni-harddrive::before {
  content: "\ead7";
}

.lni-headphone-alt::before {
  content: "\ead8";
}

.lni-headphone::before {
  content: "\ead9";
}

.lni-heart-filled::before {
  content: "\eada";
}

.lni-heart-monitor::before {
  content: "\eadb";
}

.lni-heart::before {
  content: "\eadc";
}

.lni-helicopter::before {
  content: "\eadd";
}

.lni-helmet::before {
  content: "\eade";
}

.lni-help::before {
  content: "\eadf";
}

.lni-highlight-alt::before {
  content: "\eae0";
}

.lni-highlight::before {
  content: "\eae1";
}

.lni-home::before {
  content: "\eae2";
}

.lni-hospital::before {
  content: "\eae3";
}

.lni-hourglass::before {
  content: "\eae4";
}

.lni-html5::before {
  content: "\eae5";
}

.lni-image::before {
  content: "\eae6";
}

.lni-inbox::before {
  content: "\eae7";
}

.lni-indent-decrease::before {
  content: "\eae8";
}

.lni-indent-increase::before {
  content: "\eae9";
}

.lni-infinite::before {
  content: "\eaea";
}

.lni-information::before {
  content: "\eaeb";
}

.lni-instagram-filled::before {
  content: "\eaec";
}

.lni-instagram-original::before {
  content: "\eaed";
}

.lni-instagram::before {
  content: "\eaef";
}

.lni-invention::before {
  content: "\eaee";
}

.lni-invest-monitor::before {
  content: "\eaf0";
}

.lni-investment::before {
  content: "\eaf1";
}

.lni-island::before {
  content: "\eaf2";
}

.lni-italic::before {
  content: "\eaf3";
}

.lni-java::before {
  content: "\eaf4";
}

.lni-javascript::before {
  content: "\eaf5";
}

.lni-jcb::before {
  content: "\eaf6";
}

.lni-joomla-original::before {
  content: "\eaf7";
}

.lni-joomla::before {
  content: "\eaf8";
}

.lni-jsfiddle::before {
  content: "\eaf9";
}

.lni-juice::before {
  content: "\eafa";
}

.lni-key::before {
  content: "\eafb";
}

.lni-keyboard::before {
  content: "\eafc";
}

.lni-keyword-research::before {
  content: "\eafd";
}

.lni-laptop-phone::before {
  content: "\eafe";
}

.lni-laptop::before {
  content: "\eaff";
}

.lni-laravel::before {
  content: "\eb00";
}

.lni-layers::before {
  content: "\eb01";
}

.lni-layout::before {
  content: "\eb02";
}

.lni-leaf::before {
  content: "\eb03";
}

.lni-library::before {
  content: "\eb04";
}

.lni-licencse::before {
  content: "\eb05";
}

.lni-line-dashed::before {
  content: "\eb06";
}

.lni-line-dotted::before {
  content: "\eb07";
}

.lni-line-double::before {
  content: "\eb08";
}

.lni-line-spacing::before {
  content: "\eb09";
}

.lni-line::before {
  content: "\eb0a";
}

.lni-lineicons-alt::before {
  content: "\eb0b";
}

.lni-lineicons::before {
  content: "\eb0c";
}

.lni-link::before {
  content: "\eb0e";
}

.lni-linkedin-original::before {
  content: "\eb0d";
}

.lni-linkedin::before {
  content: "\eb0f";
}

.lni-list::before {
  content: "\eb10";
}

.lni-lock-alt::before {
  content: "\eb11";
}

.lni-lock::before {
  content: "\eb12";
}

.lni-magnet::before {
  content: "\eb13";
}

.lni-magnifier::before {
  content: "\eb14";
}

.lni-mailchimp::before {
  content: "\eb15";
}

.lni-map-marker::before {
  content: "\eb16";
}

.lni-map::before {
  content: "\eb17";
}

.lni-mashroom::before {
  content: "\eb18";
}

.lni-mastercard::before {
  content: "\eb19";
}

.lni-medall-alt::before {
  content: "\eb1a";
}

.lni-medall::before {
  content: "\eb1b";
}

.lni-medium::before {
  content: "\eb1c";
}

.lni-megento::before {
  content: "\eb1d";
}

.lni-menu::before {
  content: "\eb1e";
}

.lni-mic::before {
  content: "\eb1f";
}

.lni-microphone::before {
  content: "\eb20";
}

.lni-microscope::before {
  content: "\eb21";
}

.lni-microsoft::before {
  content: "\eb22";
}

.lni-minus::before {
  content: "\eb23";
}

.lni-mobile::before {
  content: "\eb24";
}

.lni-money-location::before {
  content: "\eb25";
}

.lni-money-protection::before {
  content: "\eb26";
}

.lni-more-alt::before {
  content: "\eb27";
}

.lni-more::before {
  content: "\eb28";
}

.lni-mouse::before {
  content: "\eb29";
}

.lni-move::before {
  content: "\eb2a";
}

.lni-music::before {
  content: "\eb2b";
}

.lni-network::before {
  content: "\eb2c";
}

.lni-night::before {
  content: "\eb2d";
}

.lni-nodejs-alt::before {
  content: "\eb2e";
}

.lni-nodejs::before {
  content: "\eb2f";
}

.lni-notepad::before {
  content: "\eb30";
}

.lni-npm::before {
  content: "\eb31";
}

.lni-offer::before {
  content: "\eb32";
}

.lni-opera::before {
  content: "\eb33";
}

.lni-package::before {
  content: "\eb34";
}

.lni-page-break::before {
  content: "\eb35";
}

.lni-pagination::before {
  content: "\eb37";
}

.lni-paint-bucket::before {
  content: "\eb36";
}

.lni-paint-roller::before {
  content: "\eb38";
}

.lni-pallet::before {
  content: "\eb39";
}

.lni-paperclip::before {
  content: "\eb3a";
}

.lni-patreon::before {
  content: "\eb3b";
}

.lni-pause::before {
  content: "\eb3c";
}

.lni-paypal-original::before {
  content: "\eb3d";
}

.lni-paypal::before {
  content: "\eb3e";
}

.lni-pencil-alt::before {
  content: "\eb3f";
}

.lni-pencil::before {
  content: "\eb40";
}

.lni-phone-set::before {
  content: "\eb41";
}

.lni-phone::before {
  content: "\eb42";
}

.lni-php::before {
  content: "\eb43";
}

.lni-pie-chart::before {
  content: "\eb44";
}

.lni-pilcrow::before {
  content: "\eb45";
}

.lni-pin::before {
  content: "\eb46";
}

.lni-pinterest::before {
  content: "\eb47";
}

.lni-pizza::before {
  content: "\eb48";
}

.lni-plane::before {
  content: "\eb49";
}

.lni-plant::before {
  content: "\eb4a";
}

.lni-play-store::before {
  content: "\eb4b";
}

.lni-play::before {
  content: "\eb4c";
}

.lni-plug::before {
  content: "\eb4d";
}

.lni-plus::before {
  content: "\eb4e";
}

.lni-pointer-down::before {
  content: "\eb4f";
}

.lni-pointer-left::before {
  content: "\eb50";
}

.lni-pointer-right::before {
  content: "\eb51";
}

.lni-pointer-up::before {
  content: "\eb52";
}

.lni-pointer::before {
  content: "\eb53";
}

.lni-popup::before {
  content: "\eb54";
}

.lni-postcard::before {
  content: "\eb55";
}

.lni-pound::before {
  content: "\eb56";
}

.lni-power-switch::before {
  content: "\eb57";
}

.lni-printer::before {
  content: "\eb58";
}

.lni-producthunt::before {
  content: "\eb59";
}

.lni-protection::before {
  content: "\eb5a";
}

.lni-pulse::before {
  content: "\eb5b";
}

.lni-pyramids::before {
  content: "\eb5c";
}

.lni-python::before {
  content: "\eb5d";
}

.lni-question-circle::before {
  content: "\eb5e";
}

.lni-quora::before {
  content: "\eb5f";
}

.lni-quotation::before {
  content: "\eb60";
}

.lni-radio-button::before {
  content: "\eb61";
}

.lni-rain::before {
  content: "\eb62";
}

.lni-react::before {
  content: "\eb63";
}

.lni-reddit::before {
  content: "\eb64";
}

.lni-reload::before {
  content: "\eb65";
}

.lni-remove-file::before {
  content: "\eb66";
}

.lni-reply::before {
  content: "\eb67";
}

.lni-restaurant::before {
  content: "\eb68";
}

.lni-revenue::before {
  content: "\eb69";
}

.lni-road::before {
  content: "\eb6a";
}

.lni-rocket::before {
  content: "\eb6b";
}

.lni-rss-feed::before {
  content: "\eb6c";
}

.lni-ruler-alt::before {
  content: "\eb6d";
}

.lni-ruler-pencil::before {
  content: "\eb6e";
}

.lni-ruler::before {
  content: "\eb6f";
}

.lni-rupee::before {
  content: "\eb70";
}

.lni-sad::before {
  content: "\eb71";
}

.lni-save::before {
  content: "\eb72";
}

.lni-school-bench-alt::before {
  content: "\eb73";
}

.lni-school-bench::before {
  content: "\eb74";
}

.lni-scooter::before {
  content: "\eb75";
}

.lni-scroll-down::before {
  content: "\eb76";
}

.lni-search-alt::before {
  content: "\eb77";
}

.lni-search::before {
  content: "\eb78";
}

.lni-select::before {
  content: "\eb79";
}

.lni-seo::before {
  content: "\eb7a";
}

.lni-share-alt::before {
  content: "\eb7b";
}

.lni-share::before {
  content: "\eb7c";
}

.lni-shield::before {
  content: "\eb7d";
}

.lni-shift-left::before {
  content: "\eb7e";
}

.lni-shift-right::before {
  content: "\eb7f";
}

.lni-ship::before {
  content: "\eb80";
}

.lni-shopify::before {
  content: "\eb81";
}

.lni-shopping-basket::before {
  content: "\eb82";
}

.lni-shortcode::before {
  content: "\eb83";
}

.lni-shovel::before {
  content: "\eb84";
}

.lni-shuffle::before {
  content: "\eb85";
}

.lni-signal::before {
  content: "\eb86";
}

.lni-sketch::before {
  content: "\eb87";
}

.lni-skipping-rope::before {
  content: "\eb88";
}

.lni-skype::before {
  content: "\eb89";
}

.lni-slack::before {
  content: "\eb8a";
}

.lni-slice::before {
  content: "\eb8b";
}

.lni-slideshare::before {
  content: "\eb8c";
}

.lni-slim::before {
  content: "\eb8d";
}

.lni-smile::before {
  content: "\eb8e";
}

.lni-snapchat::before {
  content: "\eb8f";
}

.lni-sort-alpha-asc::before {
  content: "\eb90";
}

.lni-sort-amount-asc::before {
  content: "\eb91";
}

.lni-sort-amount-dsc::before {
  content: "\eb92";
}

.lni-souncloud-original::before {
  content: "\eb93";
}

.lni-soundcloud::before {
  content: "\eb94";
}

.lni-speechless::before {
  content: "\eb95";
}

.lni-spellcheck::before {
  content: "\eb96";
}

.lni-spiner-solid::before {
  content: "\eb97";
}

.lni-spinner-arrow::before {
  content: "\eb98";
}

.lni-spinner::before {
  content: "\eb99";
}

.lni-spotify-original::before {
  content: "\eb9a";
}

.lni-spotify::before {
  content: "\eb9b";
}

.lni-spray::before {
  content: "\eb9c";
}

.lni-stackoverflow::before {
  content: "\eb9d";
}

.lni-stamp::before {
  content: "\eb9e";
}

.lni-star-empty::before {
  content: "\eb9f";
}

.lni-star-filled::before {
  content: "\eba0";
}

.lni-star-half::before {
  content: "\eba1";
}

.lni-star::before {
  content: "\eba2";
}

.lni-stats-down::before {
  content: "\eba3";
}

.lni-stats-up::before {
  content: "\eba4";
}

.lni-steam::before {
  content: "\eba5";
}

.lni-sthethoscope::before {
  content: "\eba6";
}

.lni-stop::before {
  content: "\eba7";
}

.lni-strikethrough::before {
  content: "\eba8";
}

.lni-stripe::before {
  content: "\eba9";
}

.lni-stumbleupon::before {
  content: "\ebaa";
}

.lni-sun::before {
  content: "\ebab";
}

.lni-support::before {
  content: "\ebac";
}

.lni-surf-board::before {
  content: "\ebad";
}

.lni-suspect::before {
  content: "\ebae";
}

.lni-swift::before {
  content: "\ebaf";
}

.lni-syringe::before {
  content: "\ebb0";
}

.lni-tab::before {
  content: "\ebb1";
}

.lni-tag::before {
  content: "\ebb2";
}

.lni-target-customer::before {
  content: "\ebb3";
}

.lni-target-revenue::before {
  content: "\ebb4";
}

.lni-target::before {
  content: "\ebb5";
}

.lni-taxi::before {
  content: "\ebb6";
}

.lni-teabag::before {
  content: "\ebb7";
}

.lni-telegram-original::before {
  content: "\ebb8";
}

.lni-telegram::before {
  content: "\ebb9";
}

.lni-text-align-center::before {
  content: "\ebba";
}

.lni-text-align-justify::before {
  content: "\ebbb";
}

.lni-text-align-left::before {
  content: "\ebbc";
}

.lni-text-align-right::before {
  content: "\ebbd";
}

.lni-text-format-remove::before {
  content: "\ebbe";
}

.lni-text-format::before {
  content: "\ebbf";
}

.lni-thought::before {
  content: "\ebc0";
}

.lni-thumbs-down::before {
  content: "\ebc1";
}

.lni-thumbs-up::before {
  content: "\ebc2";
}

.lni-thunder-alt::before {
  content: "\ebc3";
}

.lni-thunder::before {
  content: "\ebc4";
}

.lni-ticket-alt::before {
  content: "\ebc5";
}

.lni-ticket::before {
  content: "\ebc6";
}

.lni-timer::before {
  content: "\ebc7";
}

.lni-tounge::before {
  content: "\ebc8";
}

.lni-train-alt::before {
  content: "\ebc9";
}

.lni-train::before {
  content: "\ebca";
}

.lni-trash::before {
  content: "\ebcb";
}

.lni-travel::before {
  content: "\ebcc";
}

.lni-tree::before {
  content: "\ebcd";
}

.lni-trees::before {
  content: "\ebce";
}

.lni-trello::before {
  content: "\ebcf";
}

.lni-trowel::before {
  content: "\ebd0";
}

.lni-tshirt::before {
  content: "\ebd1";
}

.lni-tumblr::before {
  content: "\ebd2";
}

.lni-twitch::before {
  content: "\ebd3";
}

.lni-twitter-filled::before {
  content: "\ebd4";
}

.lni-twitter-original::before {
  content: "\ebd5";
}

.lni-twitter::before {
  content: "\ebd6";
}

.lni-ubuntu::before {
  content: "\ebd7";
}

.lni-underline::before {
  content: "\ebd8";
}

.lni-unlink::before {
  content: "\ebd9";
}

.lni-unlock::before {
  content: "\ebda";
}

.lni-upload::before {
  content: "\ebdb";
}

.lni-user::before {
  content: "\ebdc";
}

.lni-users::before {
  content: "\ebdd";
}

.lni-ux::before {
  content: "\ebde";
}

.lni-vector::before {
  content: "\ebdf";
}

.lni-video::before {
  content: "\ebe0";
}

.lni-vimeo::before {
  content: "\ebe1";
}

.lni-visa::before {
  content: "\ebe2";
}

.lni-vk::before {
  content: "\ebe4";
}

.lni-volume-high::before {
  content: "\ebe3";
}

.lni-volume-low::before {
  content: "\ebe5";
}

.lni-volume-medium::before {
  content: "\ebe6";
}

.lni-volume-mute::before {
  content: "\ebe7";
}

.lni-volume::before {
  content: "\ebe8";
}

.lni-wallet::before {
  content: "\ebe9";
}

.lni-warning::before {
  content: "\ebea";
}

.lni-website-alt::before {
  content: "\ebeb";
}

.lni-website::before {
  content: "\ebec";
}

.lni-wechat::before {
  content: "\ebed";
}

.lni-weight::before {
  content: "\ebee";
}

.lni-whatsapp::before {
  content: "\ebef";
}

.lni-wheelbarrow::before {
  content: "\ebf0";
}

.lni-wheelchair::before {
  content: "\ebf1";
}

.lni-windows::before {
  content: "\ebf2";
}

.lni-wordpress-filled::before {
  content: "\ebf3";
}

.lni-wordpress::before {
  content: "\ebf4";
}

.lni-world-alt::before {
  content: "\ebf5";
}

.lni-world::before {
  content: "\ebf6";
}

.lni-write::before {
  content: "\ebf7";
}

.lni-yahoo::before {
  content: "\ebf8";
}

.lni-ycombinator::before {
  content: "\ebf9";
}

.lni-yen::before {
  content: "\ebfa";
}

.lni-youtube::before {
  content: "\ebfb";
}

.lni-zip::before {
  content: "\ebfc";
}

.lni-zoom-in::before {
  content: "\ebfd";
}

.lni-zoom-out::before {
  content: "\ebfe";
}

